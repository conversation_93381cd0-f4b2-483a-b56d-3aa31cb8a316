<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()
// 动态计算当前激活的菜单项，使其与浏览器URL路径保持一致
// 当路由变化时，activeMenuIndex 会自动更新，从而更新 el-menu 的高亮项
const activeMenuIndex = computed(() => route.path)
</script>

<template>
  <!-- el-container 是 Element Plus 的布局容器，通常用于构建整体页面结构 -->
  <el-container>
    <!-- el-header 用于页面头部 -->
    <el-header>
      <!-- 
        el-menu 导航菜单组件
        mode="horizontal": 水平模式
        :router="true": 启用 vue-router 模式，点击菜单项会进行路由跳转
        :default-active="activeMenuIndex": 动态绑定当前激活菜单的 index，确保刷新或直接访问URL时菜单高亮正确
       -->
      <el-menu mode="horizontal" :router="true" :default-active="activeMenuIndex">
        <!-- 
          el-menu-item 菜单项
          index: 唯一标志，在 router 模式下通常设置为对应的路由路径
         -->
        <el-menu-item index="/">首页 (Element UI)</el-menu-item>
        <el-menu-item index="/data">新闻列表 (Axios)</el-menu-item>
        <el-menu-item index="/dashboard">无人机管理大屏</el-menu-item>
        <el-menu-item index="/drone-dashboard-pro">政务数据看板</el-menu-item>
        <el-menu-item index="/about">关于</el-menu-item>
      </el-menu>
    </el-header>
    <!-- el-main 用于页面主要内容区域 -->
    <el-main>
      <!-- RouterView 是Vue Router的核心组件，用于渲染当前路由匹配到的视图组件 -->
      <RouterView />
    </el-main>
  </el-container>
</template>

<style scoped>
.el-header {
  padding: 0;
  /* position: fixed; /* 如果希望头部固定 */
  /* width: 100%; */
  /* z-index: 1000; */
}
.el-main {
  /* padding: 20px; /* 恢复Element Plus的默认或通用内边距 */
  /* background-color: #f0f2f5; /* 恢复一个通用的浅色背景 */ 
  /* min-height: calc(100vh - 60px); /* 60px 是 el-header 的大致高度 */
  /* 如果头部固定，可能需要调整 main 的 padding-top */
  /* padding-top: 60px; */ 
}
</style>
