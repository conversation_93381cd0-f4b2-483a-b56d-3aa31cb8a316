// _variables.scss

// 1. 变量 (Variables)
// Sass 使用 $ 符号定义变量，可以存储颜色、字体、尺寸等可复用的值。
$primary-color: #409EFF; // Element Plus 主题蓝
$secondary-color: #67c23a; // 绿色
$text-color: #303133;
$border-radius-base: 4px;
$font-family-base: 'Helvetica Neue', Helvetica, Arial, sans-serif;

// 2. 混合 (Mixins)
// Mixins 允许你定义一组CSS声明，然后在其他地方通过 @include 复用这组声明，可以接收参数。
@mixin flex-center($direction: row) {
  display: flex;
  flex-direction: $direction;
  justify-content: center;
  align-items: center;
}

@mixin box-shadow($color: rgba(0, 0, 0, 0.1)) {
  box-shadow: 0 2px 12px 0 $color;
}

// 3. 函数 (Functions) - Sass 也支持自定义函数，但这里先不展开

// 导出，以便其他Sass文件可以 @use 或 @import
// （在这个简单的例子中，直接被其他文件 @import 即可） 