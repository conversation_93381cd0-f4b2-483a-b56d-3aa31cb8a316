import './assets/main.css' // 导入全局CSS样式，通常用于基础样式或重置
import './assets/custom-styles.scss' // 导入我们自定义的Sass样式文件

import { createApp } from 'vue' // 从Vue中导入createApp函数，用于创建Vue应用实例
import { createPinia } from 'pinia' // 导入Pinia的创建函数 (如果项目中使用了Pinia作为状态管理器)

import App from './App.vue' // 导入根组件App.vue
import router from './router' // 导入路由配置

import ElementPlus from 'element-plus' // 完整导入Element Plus库
import 'element-plus/dist/index.css' // 导入Element Plus的CSS样式文件
import * as ElementPlusIconsVue from '@element-plus/icons-vue' // 导入所有Element Plus图标

// 创建Vue应用实例
const app = createApp(App)

// 注册Element Plus的图标组件，使其可以在全局使用
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用Pinia状态管理器 (如果项目中需要)
// app.use(createPinia()) 

// 使用Element Plus插件，使其所有组件在应用中可用
app.use(ElementPlus)

// 使用Vue Router插件，启用路由功能
app.use(router)

// 将Vue应用实例挂载到HTML页面中id为'app'的DOM元素上
// 这是Vue应用启动并显示在浏览器中的最后一步
app.mount('#app')
