<template>
  <div class="drone-dashboard-pro">
    <el-row class="title-bar">
      <el-col :span="24">
        <h1>无人机政务智能数据看板</h1>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="main-content">
      <!-- 左侧栏 -->
      <el-col :xs="24" :sm="24" :md="6" :lg="6" class="dashboard-col">
        <div class="module-box status-distribution-box">
          <div class="module-title">无人机状态分布</div>
          <div id="drone-status-chart" class="chart-container"></div>
        </div>
        <div class="module-box task-type-box">
          <div class="module-title">任务类型统计</div>
          <div id="task-type-chart" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 中间栏 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" class="dashboard-col">
        <div class="module-box real-time-data-box">
          <div class="module-title">实时飞行数据概览</div>
          <div class="real-time-stats">
            <div class="stat-item">
              <span class="label">当前飞行架次:</span>
              <span class="value">{{ 实时数据.飞行中 || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="label">平均飞行高度 (m):</span>
              <span class="value">{{ 实时数据.平均高度 || 120 }}</span>
            </div>
            <div class="stat-item">
              <span class="label">平均飞行速度 (km/h):</span>
              <span class="value">{{ 实时数据.平均速度 || 45 }}</span>
            </div>
          </div>
          <div id="flight-trends-chart" class="chart-container small-chart"></div>
        </div>
        <div class="module-box alarm-info-box">
          <div class="module-title">实时告警信息</div>
          <ul class="alarm-list">
            <li v-for="(alarm, index) in 告警列表" :key="index" :class="`level-${alarm.level}`">
              <span class="time">[{{ alarm.time }}]</span>
              <span class="content">{{ alarm.content }} (无人机: {{ alarm.droneId }})</span>
            </li>
            <li v-if="!告警列表.length">暂无告警</li>
          </ul>
        </div>
      </el-col>

      <!-- 右侧栏 -->
      <el-col :xs="24" :sm="24" :md="6" :lg="6" class="dashboard-col">
        <div class="module-box flight-duration-box">
          <div class="module-title">今日飞行统计</div>
          <div class="stat-item-large">
            <span class="label">总飞行架次:</span>
            <span class="value">{{ 今日统计.总架次 || 15 }}</span>
          </div>
          <div class="stat-item-large">
            <span class="label">总飞行时长 (h):</span>
            <span class="value">{{ 今日统计.总时长 || 28.5 }}</span>
          </div>
        </div>
        <div class="module-box area-coverage-box">
          <div class="module-title">区域覆盖分析 (示意)</div>
          <div id="area-coverage-chart" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import { PieChart, BarChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册ECharts组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  PieChart,
  BarChart,
  LineChart,
  CanvasRenderer,
])

// --- 模拟数据 --- (后续可以替换为真实API数据)
const 实时数据 = ref({ 飞行中: 5, 平均高度: 125, 平均速度: 48 })
const 今日统计 = ref({ 总架次: 22, 总时长: 35.2 })
const 告警列表 = ref([
  { time: '10:35:12', content: '电量低于20%', droneId: 'DJI-003', level: 'warning' },
  { time: '10:33:50', content: '超出预设航线范围', droneId: 'DJI-007', level: 'critical' },
  { time: '10:28:02', content: '信号弱', droneId: 'DJI-001', level: 'info' },
])

let droneStatusChart: echarts.ECharts | null = null
let taskTypeChart: echarts.ECharts | null = null
let flightTrendsChart: echarts.ECharts | null = null
let areaCoverageChart: echarts.ECharts | null = null

// --- ECharts 初始化函数 --- (仅包含选项骨架，具体数据和样式需填充)
const initCharts = () => {
  // 无人机状态分布 - 饼图
  const droneStatusElement = document.getElementById('drone-status-chart')
  if (droneStatusElement) {
    droneStatusChart = echarts.init(droneStatusElement, 'dark') // 使用深色主题
    const droneStatusOption = {
      backgroundColor: 'transparent',
      tooltip: { trigger: 'item' },
      legend: { top: 'bottom', textStyle: { color: '#ccc' } },
      series: [
        {
          name: '无人机状态',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: { borderRadius: 10, borderColor: '#1e2637', borderWidth: 2 },
          label: { show: false, position: 'center' },
          emphasis: { label: { show: true, fontSize: '20', fontWeight: 'bold' } },
          labelLine: { show: false },
          data: [
            { value: 10, name: '飞行中' },
            { value: 15, name: '待命中' },
            { value: 3, name: '充电中' },
            { value: 2, name: '故障/离线' },
          ],
        },
      ],
    }
    droneStatusChart.setOption(droneStatusOption)
  }

  // 任务类型统计 - 柱状图
  const taskTypeElement = document.getElementById('task-type-chart')
  if (taskTypeElement) {
    taskTypeChart = echarts.init(taskTypeElement, 'dark')
    const taskTypeOption = {
      backgroundColor: 'transparent',
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: [
        {
          type: 'category',
          data: ['巡检', '安防', '测绘', '物流', '应急'],
          axisTick: { alignWithLabel: true },
          axisLine: { lineStyle: { color: '#555' } },
          axisLabel: { color: '#ccc' },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: { lineStyle: { color: '#555' } },
          axisLabel: { color: '#ccc' },
          splitLine: { lineStyle: { type: 'dashed', color: '#333' } },
        },
      ],
      series: [
        {
          name: '任务数量',
          type: 'bar',
          barWidth: '60%',
          data: [30, 45, 20, 12, 8],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 1, color: '#83bff6' },
            ]),
          },
        },
      ],
    }
    taskTypeChart.setOption(taskTypeOption)
  }

  // 飞行趋势 - 简单折线图
  const flightTrendsElement = document.getElementById('flight-trends-chart')
  if (flightTrendsElement) {
    flightTrendsChart = echarts.init(flightTrendsElement, 'dark')
    const flightTrendsOption = {
      backgroundColor: 'transparent',
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['08:00', '09:00', '10:00', '11:00', '12:00'],
        boundaryGap: false,
        axisLine: { lineStyle: { color: '#555' } },
        axisLabel: { color: '#ccc' },
      },
      yAxis: {
        type: 'value',
        name: '架次',
        axisLine: { lineStyle: { color: '#555' } },
        axisLabel: { color: '#ccc' },
        splitLine: { lineStyle: { type: 'dashed', color: '#333' } },
      },
      grid: { top: '15%', left: '3%', right: '4%', bottom: '3%', containLabel: true },
      series: [
        {
          data: [3, 5, 4, 6, 5],
          type: 'line',
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(58,77,233,0.8)' },
              { offset: 1, color: 'rgba(58,77,233,0.3)' },
            ]),
          },
        },
      ],
    }
    flightTrendsChart.setOption(flightTrendsOption)
  }

  // 区域覆盖分析 - 示意图 (简单饼图占位)
  const areaCoverageElement = document.getElementById('area-coverage-chart')
  if (areaCoverageElement) {
    areaCoverageChart = echarts.init(areaCoverageElement, 'dark')
    const areaCoverageOption = {
      backgroundColor: 'transparent',
      tooltip: { trigger: 'item' },
      series: [
        {
          name: '区域覆盖',
          type: 'pie',
          radius: '65%',
          center: ['50%', '50%'],
          data: [
            { value: 75, name: '已覆盖' },
            { value: 25, name: '未覆盖' },
          ].sort((a, b) => a.value - b.value),
          roseType: 'radius',
          label: { color: 'rgba(255, 255, 255, 0.7)' },
          labelLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.3)' },
            smooth: 0.2,
            length: 10,
            length2: 20,
          },
          itemStyle: { shadowBlur: 200, shadowColor: 'rgba(0, 0, 0, 0.5)' },
        },
      ],
    }
    areaCoverageChart.setOption(areaCoverageOption)
  }
}

const resizeCharts = () => {
  droneStatusChart?.resize()
  taskTypeChart?.resize()
  flightTrendsChart?.resize()
  areaCoverageChart?.resize()
}

onMounted(async () => {
  await nextTick() // 确保DOM渲染完成
  initCharts()
  window.addEventListener('resize', resizeCharts)
  // 可以在这里启动定时器更新实时数据和告警列表
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeCharts)
  droneStatusChart?.dispose()
  taskTypeChart?.dispose()
  flightTrendsChart?.dispose()
  areaCoverageChart?.dispose()
})
</script>

<style scoped>
.drone-dashboard-pro {
  background-color: #0f1423; /* 深色科技蓝背景 */
  color: #c0c4cc; /* 浅色文字 */
  padding: 20px;
  min-height: 100vh;
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.title-bar h1 {
  color: #409eff; /* Element Plus 主题色 */
  text-align: center;
  font-size: 2.5em;
  margin-bottom: 20px;
  letter-spacing: 2px;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
}

.dashboard-col {
  margin-bottom: 20px;
}

.module-box {
  background-color: rgba(30, 38, 55, 0.7); /* 半透明深色背景 */
  border: 1px solid #2a3557; /* 科技感边框 */
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3) inset;
  position: relative;
}

/* 给模块盒子添加一个模拟的"光角"效果 */
.module-box::before,
.module-box::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border-color: #409eff;
  border-style: solid;
}
.module-box::before {
  top: -2px;
  left: -2px;
  border-width: 2px 0 0 2px;
}
.module-box::after {
  bottom: -2px;
  right: -2px;
  border-width: 0 2px 2px 0;
}

.module-title {
  color: #a7b4ce;
  font-size: 1.2em;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #2a3557;
  text-align: center;
}

.chart-container {
  width: 100%;
  height: 280px; /* 根据需要调整图表高度 */
}
.small-chart {
  height: 180px;
}

.real-time-stats .stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  font-size: 1em;
  border-bottom: 1px dashed #2a3557;
}
.real-time-stats .stat-item:last-child {
  border-bottom: none;
}
.real-time-stats .label {
  color: #8c9db3;
}
.real-time-stats .value {
  color: #409eff;
  font-weight: bold;
  font-size: 1.1em;
}

.stat-item-large {
  text-align: center;
  margin: 20px 0;
}
.stat-item-large .label {
  display: block;
  color: #8c9db3;
  font-size: 1em;
  margin-bottom: 8px;
}
.stat-item-large .value {
  color: #409eff;
  font-weight: bold;
  font-size: 2em;
}

.alarm-list {
  list-style: none;
  padding: 0;
  margin: 0;
  height: 180px; /* 根据需要调整 */
  overflow-y: auto; /* 内容超出时滚动 */
}
.alarm-list li {
  padding: 6px 3px;
  border-bottom: 1px solid #2a3557;
  font-size: 0.9em;
  display: flex;
  align-items: center;
}
.alarm-list li:last-child {
  border-bottom: none;
}
.alarm-list .time {
  color: #777;
  margin-right: 10px;
  white-space: nowrap;
}
.alarm-list .content {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.alarm-list .level-critical {
  color: #f56c6c; /* 红色 - 严重 */
  font-weight: bold;
}
.alarm-list .level-warning {
  color: #e6a23c; /* 黄色 - 警告 */
}
.alarm-list .level-info {
  color: #909399; /* 灰色 - 一般信息 */
}

/* 自定义滚动条样式 (可选，增加科技感) */
.alarm-list::-webkit-scrollbar {
  width: 6px;
}
.alarm-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}
.alarm-list::-webkit-scrollbar-thumb {
  background: #38456f;
  border-radius: 3px;
}
.alarm-list::-webkit-scrollbar-thumb:hover {
  background: #409eff;
}

/* --- 响应式布局调整 --- */
@media (max-width: 991px) {
  /* 对应 Element Plus 的 sm 及 xs 断点，列开始堆叠 */
  .title-bar h1 {
    font-size: 2em; /* 缩小主标题 */
    margin-bottom: 15px;
  }

  .module-title {
    font-size: 1.1em; /* 缩小模块标题 */
  }

  .dashboard-col {
    margin-bottom: 15px; /* 堆叠时减小列间距 */
  }

  .module-box {
    padding: 10px;
  }

  .real-time-stats .stat-item,
  .alarm-list li {
    font-size: 0.9em; /* 缩小列表项字体 */
  }

  .stat-item-large .label {
    font-size: 0.9em;
  }
  .stat-item-large .value {
    font-size: 1.8em;
  }

  .chart-container {
    height: 250px; /* 可以适当减小图表默认高度 */
  }
  .small-chart {
    height: 160px;
  }
  .alarm-list {
    height: 150px; /* 适当减小告警列表高度 */
  }
}

@media (max-width: 767px) {
  /* 对应 Element Plus 的 xs 断点，进一步细化 */
  .title-bar h1 {
    font-size: 1.8em;
    letter-spacing: 1px;
  }

  .real-time-stats .stat-item .value,
  .stat-item-large .value {
    font-size: 1.6em;
  }

  .stat-item-large .label {
    font-size: 0.85em;
  }

  /* 如果需要，可以进一步减小图表高度或调整其他元素 */
  .chart-container {
    height: 220px;
  }
  .small-chart {
    height: 140px;
  }
}
</style>
