// custom-styles.scss

// 导入变量和 mixins 文件 (注意，导入 partial 文件时可以省略下划线和 .scss 后缀)
@import 'variables';

// 使用 Sass 特性编写样式

// 1. 使用变量
body {
  // 注意：此处的 body 样式可能被 src/assets/base.css 或其他全局样式覆盖
  // 这里仅为演示变量使用，实际项目中应统一规划全局样式
  // font-family: $font-family-base; 
}

.custom-button {
  padding: 10px 20px;
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: $border-radius-base;
  cursor: pointer;
  @include box-shadow(rgba($primary-color, 0.3)); // 使用 mixin 并传递参数

  &:hover {
    background-color: darken($primary-color, 10%); // Sass 内置函数 darken()
  }
}

// 2. 嵌套 (Nesting)
.panel {
  padding: 15px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: $border-radius-base;

  .panel-header {
    font-size: 1.2em;
    color: $text-color;
    margin-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 8px;
  }

  .panel-body {
    font-size: 0.9em;
    line-height: 1.6;

    p {
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  // 使用 mixin
  &.centered-content {
    @include flex-center(column);
  }
} 