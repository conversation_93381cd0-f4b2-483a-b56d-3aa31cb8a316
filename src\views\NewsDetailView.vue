<template>
  <div class="news-detail-view" v-if="newsItem">
    <el-page-header @back="goBack" :content="newsItem.title || '新闻详情'"></el-page-header>
    <el-card class="news-detail-card">
      <h1>{{ newsItem.title }}</h1>
      <div class="meta-info">
        <span>来源: {{ newsItem.source }}</span>
        <span>时间: {{ newsItem.ctime }}</span>
      </div>
      <el-divider />
      <div v-if="newsItem.picUrl" class="image-container">
        <el-image :src="newsItem.picUrl" fit="contain" style="max-width: 100%; height: auto;" />
      </div>
      <div class="news-body-content">
        <p v-if="newsItem.description">{{ newsItem.description }}</p>
        <p v-else>
          <em>此新闻未提供详细描述。您可以 <a :href="newsItem.url" target="_blank" rel="noopener noreferrer">点击此处阅读原文</a> 获取更多信息。
          </em>
        </p>
        <!-- 此处可以预留给未来可能的完整新闻正文 -->
      </div>
    </el-card>
  </div>
  <div v-else-if="loading" class="loading-state">
    <el-skeleton :rows="10" animated />
  </div>
  <div v-else class="not-found">
    <el-empty description="未找到该新闻或新闻ID无效"></el-empty>
    <el-button @click="goBack">返回列表</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElPageHeader, ElCard, ElDivider, ElImage, ElEmpty, ElSkeleton, ElButton, ElIcon } from 'element-plus';
// 假设 NewsItem 接口与 DataView.vue 中的定义一致
// 如果不一致，需要单独定义或从共享文件导入
interface NewsItem {
  id: string;
  ctime: string;
  title: string;
  description?: string;
  source: string;
  picUrl: string;
  url: string;
}

const route = useRoute();
const router = useRouter();
const newsItem = ref<NewsItem | null>(null);
const loading = ref(true);

const findNewsItemById = (id: string): NewsItem | undefined => {
  // 尝试从 localStorage 获取 newsItems (由DataView存储)
  const storedNews = localStorage.getItem('newsItemsList');
  if (storedNews) {
    const items: NewsItem[] = JSON.parse(storedNews);
    return items.find(item => item.id === id);
  }
  return undefined;
};

const loadNewsDetail = () => {
  loading.value = true;
  const newsId = route.params.id as string;
  if (newsId) {
    const foundItem = findNewsItemById(newsId);
    if (foundItem) {
      newsItem.value = foundItem;
    } else {
      newsItem.value = null; // 清除旧数据
      ElMessage.error('无法找到该新闻的详细信息。');
    }
  } else {
    ElMessage.error('无效的新闻ID。');
    newsItem.value = null;
  }
  loading.value = false;
};

onMounted(() => {
  loadNewsDetail();
});

// 监听路由参数变化，以防用户在详情页之间切换
watch(() => route.params.id, (newId) => {
  if (newId) {
    loadNewsDetail();
  }
});

const goBack = () => {
  router.back(); // 或者 router.push('/data')
};

</script>

<style scoped>
.news-detail-view {
  padding: 20px;
}
.news-detail-card {
  margin-top: 20px;
}
.meta-info {
  font-size: 0.9em;
  color: #909399;
  margin-bottom: 15px;
}
.meta-info span {
  margin-right: 15px;
}
.image-container {
  margin-bottom: 20px;
  text-align: center; /* 图片居中 */
}
.news-body-content {
  line-height: 1.8;
  font-size: 1em;
}
.news-body-content p {
  margin-bottom: 1em;
}
.loading-state, .not-found {
  padding: 20px;
  text-align: center;
}
</style> 