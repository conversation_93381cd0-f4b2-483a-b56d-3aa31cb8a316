import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
// 导入各个视图组件
// HomeView, DataView等是直接导入，会在初始包中加载
import HomeView from '../views/HomeView.vue'
import DataView from '../views/DataView.vue'
import DashboardView from '../views/DashboardView.vue'
import NewsDetailView from '../views/NewsDetailView.vue'
import DroneDashboardProView from '../views/DroneDashboardProView.vue'

// 定义路由记录数组
const routes: Array<RouteRecordRaw> = [
  {
    path: '/', // 路由路径
    name: 'home', // 路由名称，方便编程式导航
    component: HomeView // 该路由对应的组件
  },
  {
    path: '/about',
    name: 'about',
    // 路由懒加载（动态导入）：
    // 只有当用户首次访问 '/about' 路径时，才会异步加载 AboutView.vue 组件的代码。
    // 这有助于减小初始包体积，加快应用启动速度。
    // 打包工具会将懒加载的组件分割成独立的代码块 (chunk)。
    component: () => import('../views/AboutView.vue')
  },
  {
    path: '/data',
    name: 'data', // 对应新闻列表页
    component: DataView
  },
  {
    path: '/dashboard',
    name: 'dashboard', // 对应第一个无人机管理大屏
    component: DashboardView
  },
  {
    path: '/news/:id', // 动态路由，':id' 是一个动态段，会作为参数传递给组件
    name: 'news-detail', // 新闻详情页
    component: NewsDetailView,
    props: true // 将路由参数 (如 :id) 作为 props 传递给 NewsDetailView 组件 (可选，也可以用 useRoute().params.id 获取)
  },
  {
    path: '/drone-dashboard-pro',
    name: 'drone-dashboard-pro', // 对应新的"酷炫"无人机看板
    component: DroneDashboardProView
  }
]

// 创建路由实例
const router = createRouter({
  // history 模式：使用 HTML5 History API 来实现不带 # 的"干净"URL。
  // import.meta.env.BASE_URL 是 Vite 提供的环境变量，通常是 '/'
  history: createWebHistory(import.meta.env.BASE_URL),
  routes // ES6缩写，等同于 routes: routes
})

// 导出路由实例，供 main.ts 中使用
export default router
