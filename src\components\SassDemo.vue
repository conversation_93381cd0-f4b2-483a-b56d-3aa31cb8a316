<template>
  <div class="sass-demo-container">
    <h3>Sass/SCSS 使用示例</h3>
    
    <button class="custom-button">自定义Sass按钮</button>

    <div class="panel" style="margin-top: 20px;">
      <div class="panel-header">面板标题 (Sass嵌套)</div>
      <div class="panel-body">
        <p>这是面板内容的第一段。使用了在 <code>custom-styles.scss</code> 中定义的嵌套样式。</p>
        <p>Sass的嵌套使得层级关系更清晰。</p>
      </div>
    </div>

    <div class="panel centered-content" style="margin-top: 20px;">
      <div class="panel-header">居中内容的面板</div>
      <div class="panel-body">
        <p>这个面板使用了 <code>@include flex-center(column)</code> 这个mixin。</p>
      </div>
    </div>

    <div class="component-specific-style">
      <h4>组件内 SCSS 样式</h4>
      <p>这段文字的样式是在本组件的 <code>&lt;style lang="scss"&gt;</code> 块中定义的。</p>
      <ul>
        <li>列表项 1</li>
        <li>列表项 2</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
// 该组件目前没有特定的脚本逻辑
</script>

<style lang="scss" scoped>
// 在Vue组件的style块中使用SCSS

// 明确导入全局Sass变量文件，使其在此组件的样式块中可用
@import '@/assets/_variables.scss'; 

$component-padding: 15px;
$highlight-color: #ffc107; // 黄色

.sass-demo-container {
  padding: $component-padding;
  border: 1px solid #ddd;
  border-radius: $border-radius-base; // 使用来自_variables.scss的全局变量
  margin-top: 20px;
  background-color: #fff;

  h3 {
    color: $primary-color; // 现在 $primary-color 来自导入的 _variables.scss
    margin-bottom: 15px;
  }
}

.component-specific-style {
  margin-top: $component-padding;
  padding: $component-padding;
  background-color: lighten($highlight-color, 35%); // 使用Sass函数和局部变量
  border-left: 5px solid $highlight-color;

  h4 {
    margin-bottom: 10px;
  }

  ul {
    list-style-type: square;
    padding-left: 20px;
    li {
      margin-bottom: 5px;
      &:hover {
        color: darken($highlight-color, 20%);
        cursor: pointer;
      }
    }
  }
}

</style> 