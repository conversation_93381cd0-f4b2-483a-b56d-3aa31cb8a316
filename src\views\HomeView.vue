<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import {Promotion, Calendar, Comment, House} from '@element-plus/icons-vue'; // 导入图标
import SassDemo from '@/components/SassDemo.vue'; // 导入SassDemo组件

const greet = () => {
  ElMessage.success('你好，Element Plus!');
};

const activeTab = ref('features');
const carouselHeight = ref('230px'); // 调整走马灯高度
const currentDate = ref(new Date()); // 为日历组件定义 currentDate

</script>

<template>
  <div class="home-view-enhanced">
    <el-card shadow="never" class="page-header-card">
      <h1>欢迎来到首页</h1>
      <p class="subtitle">Element Plus 组件应用展示</p>
      <el-button type="primary" :icon="Promotion" @click="greet">感受 Element Plus</el-button>
    </el-card>

    <el-row :gutter="20" class="main-content">
      <el-col :md="14">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span><el-icon><House /></el-icon> 特色内容展示</span>
            </div>
          </template>
          <el-carousel :interval="4000" type="card" :height="carouselHeight">
            <el-carousel-item v-for="item in 3" :key="item">
              <div :class="`carousel-item-content item-${item}`">
                <h3>特色内容 {{ item }}</h3>
                <p>这里是一些特色内容的简要描述。</p>
              </div>
            </el-carousel-item>
          </el-carousel>
        </el-card>

        <el-card class="box-card" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span><el-icon><Comment /></el-icon> 信息板块</span>
            </div>
          </template>
          <el-tabs v-model="activeTab" class="demo-tabs">
            <el-tab-pane label="功能特性" name="features">
              <p>这里是关于功能特性的详细介绍...</p>
              <el-alert
                title="Element Plus 功能强大"
                type="success"
                description="提供了丰富的组件和灵活的配置选项。"
                show-icon
                :closable="false"
                style="margin-top: 10px;"
              />
            </el-tab-pane>
            <el-tab-pane label="最新动态" name="updates">
              <p>这里是最新的动态和更新日志...</p>
              <el-timeline style="margin-top:10px;">
                <el-timeline-item timestamp="2024/05/10" placement="top">
                  <h4>新版本发布</h4>
                  <p>V1.2.3 已发布，包含多项优化。</p>
                </el-timeline-item>
                <el-timeline-item timestamp="2024/04/28" placement="top">
                  <h4>重要更新</h4>
                  <p>修复了若干已知问题。</p>
                </el-timeline-item>
              </el-timeline>
            </el-tab-pane>
            <el-tab-pane label="用户反馈" name="feedback">
              <p>这里是用户反馈和建议的区域...</p>
               <el-empty description="暂无反馈" />
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <el-card class="box-card" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>SCSS 使用示例</span>
            </div>
          </template>
          <SassDemo />
        </el-card>
      </el-col>

      <el-col :md="10">
        <el-card class="box-card calendar-card">
          <template #header>
            <div class="card-header">
              <span><el-icon><Calendar /></el-icon> 今日日程</span>
            </div>
          </template>
          <el-calendar v-model="currentDate">
            <template #header="{ date, prev, next, today }">
              <div class="calendar-header-custom">
                <span class="calendar-title">{{ date }}</span>
                <el-button-group class="calendar-actions">
                  <el-button size="small" @click="prev">上一月</el-button>
                  <el-button size="small" @click="today">今天</el-button>
                  <el-button size="small" @click="next">下一月</el-button>
                </el-button-group>
              </div>
            </template>
          </el-calendar>
        </el-card>
         <el-card class="box-card" style="margin-top:20px;">
          <template #header>
            <div class="card-header">
              <span>任务进度</span>
            </div>
          </template>
          <div style="padding: 10px 0;">
            <p>项目A进度:</p>
            <el-progress :percentage="75" status="success" />
            <p style="margin-top:10px;">模块B开发:</p>
            <el-progress :percentage="40" />
            <p style="margin-top:10px;">文档撰写:</p>
            <el-progress :percentage="90" status="warning" />
          </div>
        </el-card>
      </el-col>
    </el-row>

  </div>
</template>

<style scoped>
.home-view-enhanced {
  padding: 20px;
  background-color: #f4f6f8; /* 更柔和的背景色 */
}

.page-header-card {
  margin-bottom: 20px;
  text-align: center;
}
.page-header-card h1 {
  margin-bottom: 8px;
  font-size: 2em;
  color: #303133;
}
.subtitle {
  color: #606266;
  margin-bottom: 15px;
  font-size: 1em;
}

.box-card {
  /* 卡片默认样式已由Element Plus提供，可按需覆盖 */
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}
.card-header .el-icon {
  margin-right: 8px;
  font-size: 1.2em;
}

.carousel-item-content {
  color: white;
  font-size: 14px;
  opacity: 0.85;
  line-height: 1.5;
  min-height: 200px; /* 确保内容区高度，与carouselHeight对应 */
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
}
.carousel-item-content.item-1 { background-color: #007bff; }
.carousel-item-content.item-2 { background-color: #28a745; }
.carousel-item-content.item-3 { background-color: #ffc107; color: #333;}
.carousel-item-content h3 {
  font-size: 1.8em;
  margin-bottom: 10px;
}

.demo-tabs > .el-tabs__content {
  padding: 20px;
  color: #6b778c;
  font-size: 16px;
  font-weight: 600;
}

.el-calendar {
  --el-calendar-selected-bg-color: var(--el-color-primary-light-9);
}

/* 为日历卡片定制样式 */
.calendar-card :deep(.el-card__body) {
  padding: 5px; /* 稍微调整，如果上面改动后还是太挤，可以再减小或设为0 */
}

/* 自定义日历头部样式 */
.calendar-header-custom {
  display: flex;
  flex-direction: column; /* 让标题和按钮组垂直排列 */
  align-items: center; /* 居中对齐 */
  gap: 10px; /* 标题和按钮组之间的间距 */
  padding-bottom: 10px; /* 头部和日历主体之间的间距 */
}

.calendar-title {
  font-size: 1.1em;
  font-weight: 500;
}

.calendar-actions .el-button {
  /* 可以根据需要调整按钮样式 */
}

/* 微调日历主体内星期表头的样式以防止文字重叠 */
.calendar-card :deep(.el-calendar__header) {
  /* 如果需要，也可以调整自定义头部的padding，确保与body协调 */
  /* padding-bottom: 12px; */ 
}

.calendar-card :deep(.el-calendar__body) {
  padding: 8px 0px; /* 进一步减少body的左右padding，给日期部分更多空间，上下padding可以保留一点 */
}

.calendar-card :deep(.el-calendar-table thead th) {
  padding: 2px 0; /* 减小星期表头单元格的上下内边距 */
  font-size: 0.8em; /* 显著减小星期表头的字体大小 */
  /* white-space: nowrap; /* 通常不需要，因为是缩写 */
}

/* 如果日期数字也拥挤，可以类似调整 */
/*
.calendar-card :deep(.el-calendar-day) {
  padding: 4px;
  font-size: 0.85em;
  min-height: 40px; 
}
*/

</style>
