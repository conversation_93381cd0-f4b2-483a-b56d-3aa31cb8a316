@import './base.css';

/* 
  对根应用元素 #app 的基本约束。
  在本项目中，大部分布局由 App.vue 内的 Element Plus 容器控制，
  这里的样式可能影响范围有限，或者主要在某些特定情况下作为后备。
*/
#app {
  /* max-width: 1280px; /* 限制应用最大宽度，如果希望应用内容始终充满视口，可以注释或移除此行 */
  /* margin: 0 auto; /* 与max-width配合使用，使内容在达到最大宽度时居中 */
  /* padding: 2rem; /* 应用的全局内边距，但很多页面组件可能会有自己的padding */
  font-weight: normal; /* 确保字体权重是正常的，避免不必要的加粗 */
  height: 100vh; /* 使#app至少占据整个视口高度，有助于内部元素使用百分比高度 */
  overflow-x: hidden; /* 防止意外的水平溢出 */
}

/* 
  默认的链接样式和 .green 类的样式。
  hsla(160, 100%, 37%, 1) 是Vue的标志性绿色。
*/
a,
.green {
  text-decoration: none; /* 移除链接下划线 */
  color: hsla(160, 100%, 37%, 1);
  transition: color 0.4s; /* 颜色变化时的平滑过渡 */
  /* padding: 3px; /* 如果需要，可以给链接一些内边距 */
}

/* 鼠标悬停在链接上时的样式 */
@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2); /* 悬停时添加浅绿色背景 */
  }
}

/* 
  以下媒体查询中的样式是Vue默认模板中用于桌面端布局的示例。
  在本项目中，由于我们使用了 Element Plus 的 el-container 和 el-menu 进行整体布局，
  这些样式可能不再完全适用或已被覆盖。
  为了避免潜在的布局冲突和简化理解，可以考虑移除或重写这些特定于旧模板的布局规则。
  暂时注释掉它们，以便初学者了解其原始用途，但知道它们在当前项目中可能不起主要作用。
*/
/*
@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}
*/
