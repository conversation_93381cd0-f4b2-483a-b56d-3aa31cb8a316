<template>
  <div class="drone-dashboard">
    <header class="dashboard-header">
      <h1>无人机政务智能数据看板</h1>
    </header>
    <main class="dashboard-main">
      <div class="widget-container">
        <div class="widget">
          <h2>核心指标</h2>
          <div class="metric-item">
            <span>无人机总数:</span>
            <span class="metric-value">{{ coreMetrics.totalDrones }}</span>
          </div>
          <div class="metric-item">
            <span>在线无人机:</span>
            <span class="metric-value">{{ coreMetrics.onlineDrones }}</span>
          </div>
          <div class="metric-item">
            <span>今日飞行架次:</span>
            <span class="metric-value">{{ coreMetrics.flightsToday }}</span>
          </div>
        </div>
        <div class="widget">
          <h2>任务与告警</h2>
          <div class="metric-item">
            <span>任务成功率:</span>
            <span class="metric-value">{{ missionStats.successRate }}%</span>
          </div>
          <div class="metric-item">
            <span>活跃告警数:</span>
            <span class="metric-value alert-value">{{ missionStats.activeAlerts }}</span>
          </div>
          <p>近期任务列表占位</p>
        </div>
        <div class="widget">
          <h2>无人机型号分布</h2>
          <p>
            饼图占位符 (例如：DJI Mavic 3: 40%, Phantom 4 Pro: 30%, Autel EVO II: 20%, 其他: 10%)
          </p>
          <!-- 建议后续集成图表库如 Chart.js, ECharts, ApexCharts 等 -->
        </div>
        <div class="widget">
          <h2>实时飞行监控</h2>
          <p>地图/无人机列表占位符</p>
          <!-- 建议后续集成地图库如 Leaflet, Mapbox GL JS 或列表组件 -->
        </div>
      </div>
    </main>
    <footer class="dashboard-footer">
      <p>&copy; 2024 无人机政务看板</p>
    </footer>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'DroneDashboard',
  // 在这里添加组件的逻辑，例如 data, methods, computed properties 等
  data() {
    return {
      coreMetrics: {
        totalDrones: 125,
        onlineDrones: 88,
        flightsToday: 230,
      },
      missionStats: {
        successRate: 98.5,
        activeAlerts: 3,
      },
      // 示例：无人机型号数据，可用于图表
      droneModelsData: [
        { name: 'DJI Mavic 3', count: 50 },
        { name: 'Phantom 4 Pro', count: 38 },
        { name: 'Autel EVO II', count: 25 },
        { name: '其他', count: 12 },
      ],
    }
  },
  mounted() {
    // 示例生命周期钩子
    console.log('DroneDashboard component mounted.')
  },
  methods: {
    // 示例方法
  },
})
</script>

<style scoped>
.drone-dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  background-color: #f0f2f5; /* 更现代的浅灰色背景 */
  color: #333;
}

.dashboard-header {
  background-color: #001529; /* 深蓝色，常用于管理后台头部 */
  color: white;
  padding: 1rem 1.5rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dashboard-header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.dashboard-main {
  flex-grow: 1;
  padding: 1.5rem;
}

.widget-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem; /* 部件之间的间距 */
}

.widget {
  background-color: white;
  border-radius: 8px; /* 更圆润的边角 */
  padding: 1.5rem;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24); /* 细微的阴影效果 */
  flex: 1 1 300px; /* Flex 响应式：增长、收缩、基础宽度 */
  /* 最小宽度 300px, 会增长以填充空间，必要时会收缩 */
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease; /* 添加悬停效果 */
}

.widget:hover {
  transform: translateY(-5px);
  box-shadow:
    0 4px 10px rgba(0, 0, 0, 0.15),
    0 2px 5px rgba(0, 0, 0, 0.2);
}

.widget h2 {
  margin-top: 0;
  color: #001529; /* 与头部颜色呼应 */
  font-size: 1.3rem;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0; /* 更浅的分割线 */
  padding-bottom: 0.75rem;
  margin-bottom: 1rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 1rem;
  color: #595959;
}

.metric-item:not(:last-child) {
  border-bottom: 1px dashed #e8e8e8; /* 项目之间的分隔线 */
}

.metric-value {
  font-weight: bold;
  font-size: 1.2rem;
  color: #333;
}

.alert-value {
  color: #f5222d; /* 醒目的红色用于告警 */
  font-weight: bold;
}

.widget p {
  font-size: 1rem;
  line-height: 1.6;
  color: #595959;
}

.dashboard-footer {
  background-color: #e9ecef; /* 浅灰色页脚 */
  color: #495057; /* 深灰色文字 */
  text-align: center;
  padding: 1rem;
  font-size: 0.9rem;
  border-top: 1px solid #dee2e6;
}

/* 响应式调整 */
@media (max-width: 992px) {
  /* 调整中等屏幕的断点 */
  .widget {
    flex-basis: calc(50% - 0.75rem); /* 中等屏幕上两列，考虑间距 */
  }
}

@media (max-width: 600px) {
  /* 调整小屏幕的断点 */
  .widget {
    flex-basis: 100%; /* 小屏幕上一列 */
  }
  .dashboard-header h1 {
    font-size: 1.5rem;
  }
  .dashboard-main {
    padding: 1rem;
  }
  .widget {
    padding: 1rem;
  }
}
</style>
