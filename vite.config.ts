// @ts-nocheck
import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

// Vite 配置参考: https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(), // Vue 3 单文件组件 (SFC) 支持
    vueJsx(), // JSX/TSX 支持 (如果需要)
    vueDevTools(), // Vue开发者工具集成，方便调试
  ],
  resolve: {
    // 路径别名配置，例如 '@' 指向 './src' 目录
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  // 开发服务器配置
  server: {
    // 代理配置，用于解决开发环境下的跨域问题
    proxy: {
      // 当请求路径以 '/juhe' 开头时，会被代理到 target 指定的域名
      '/juhe': {
        target: 'http://apis.juhe.cn', // 目标API服务器域名
        changeOrigin: true, // 设置为true，服务器接收到的请求头中的host字段会是目标域名，对于虚拟主机站点是必需的
        // 重写路径：将请求路径中的 '/juhe' 前缀移除，再发送到目标服务器
        // 例如：前端请求 '/juhe/fapigx/internet_news/query' -> 代理后实际请求 'http://apis.juhe.cn/fapigx/internet_news/query'
        rewrite: (path) => path.replace(/^\/juhe/, '') 
      }
      // 如果有其他需要代理的API，可以在这里继续添加
    }
  }
})
