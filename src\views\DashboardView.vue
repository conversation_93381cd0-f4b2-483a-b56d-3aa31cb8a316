<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <!-- 左侧：无人机状态概览 -->
      <el-col :span="6">
        <el-card class="box-card status-card">
          <template #header>
            <div class="card-header">
              <span>无人机状态概览</span>
            </div>
          </template>
          <div class="status-overview">
            <div class="status-item">
              <span class="status-label">在线总数:</span>
              <span class="status-value online">{{ droneStats.totalOnline }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">飞行中:</span>
              <span class="status-value flying">{{ droneStats.flying }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">待命中:</span>
              <span class="status-value standby">{{ droneStats.standby }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">离线/故障:</span>
              <span class="status-value offline">{{ droneStats.offline }}</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：地图区域 -->
      <el-col :span="18">
        <el-card class="box-card map-card">
          <template #header>
            <div class="card-header">
              <span>实时地图监控 (浙江省海宁市)</span>
            </div>
          </template>
          <div id="amap-container" class="map-container-actual"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, shallowRef, computed } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';

// 模拟无人机位置数据
interface Drone {
  id: string;
  name: string;
  position: [number, number]; // [经度, 纬度]
  status: 'flying' | 'standby' | 'offline';
}

const mockDrones = ref<Drone[]>([
  { id: 'DJI-001', name: '无人机1 (飞行中)', position: [120.6850, 30.5360], status: 'flying' },
  { id: 'DJI-002', name: '无人机2 (待命中)', position: [120.6950, 30.5300], status: 'standby' },
  { id: 'DJI-003', name: '无人机3 (飞行中)', position: [120.6780, 30.5250], status: 'flying' },
  { id: 'DJI-004', name: '无人机4 (离线)', position: [120.7000, 30.5400], status: 'offline' },
  { id: 'DJI-005', name: '无人机5 (待命中)', position: [120.6800, 30.5450], status: 'standby' },
  { id: 'DJI-006', name: '无人机6 (飞行中)', position: [120.7100, 30.5320], status: 'flying' },
  { id: 'DJI-007', name: '无人机7 (待命中)', position: [120.6920, 30.5180], status: 'standby' },
  { id: 'DJI-008', name: '无人机8 (待命中)', position: [120.6750, 30.5380], status: 'standby' },
  { id: 'DJI-009', name: '无人机9 (待命中)', position: [120.7050, 30.5220], status: 'standby' },
]);

// 根据 mockDrones 动态计算状态统计
const droneStats = computed(() => {
  const stats = {
    totalOnline: 0,
    flying: 0,
    standby: 0,
    offline: 0,
  };
  for (const drone of mockDrones.value) {
    if (drone.status === 'flying') {
      stats.flying++;
      stats.totalOnline++;
    } else if (drone.status === 'standby') {
      stats.standby++;
      stats.totalOnline++;
    } else if (drone.status === 'offline') {
      stats.offline++;
    }
  }
  return stats;
});

let map: any = shallowRef(null); // 使用 shallowRef 存储地图实例

// IMPORTANT: 请替换为您自己的高德 API Key 和安全密钥
const AMAP_KEY = '41d2bf648ea23c7b556223a363ff3d46';
const AMAP_SECURITY_CODE = 'f425ad9b0c44faef790cbb057ddefe8e';

const initMap = async () => {
  try {
    (window as any)._AMapSecurityConfig = {
      securityJsCode: AMAP_SECURITY_CODE,
    };
    const AMap = await AMapLoader.load({
      key: AMAP_KEY,
      version: "2.0",
      plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.HawkEye', 'AMap.MapType', 'AMap.Marker'], // 添加 AMap.Marker 插件
    });

    map.value = new AMap.Map("amap-container", {
      zoom: 12, // 缩放级别
      center: [120.6895, 30.5338], // 海宁市的经纬度大致坐标
      viewMode: '2D', // 使用2D视图
    });

    // 添加地图控件
    map.value.addControl(new AMap.Scale());
    map.value.addControl(new AMap.ToolBar());
    map.value.addControl(new AMap.HawkEye({isOpen:true}));
    map.value.addControl(new AMap.MapType());

    // 添加无人机标记
    addDroneMarkers(AMap);

  } catch (e) {
    console.error("高德地图加载失败: ", e);
  }
};

const addDroneMarkers = (AMap: any) => {
  if (!map.value) return;

  const svgPin = (color: string) => 
    `<svg width="24" height="36" viewBox="0 0 24 36" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 0C5.373 0 0 5.373 0 12C0 21 12 36 12 36C12 36 24 21 24 12C24 5.373 18.627 0 12 0Z" fill="${color}"/>
      <circle cx="12" cy="12" r="5" fill="white"/>
      <circle cx="12" cy="12" r="3" fill="${color}"/>
    </svg>`;

  const markerSVGContent = {
    flying: svgPin('green'),
    standby: svgPin('blue'),
    offline: svgPin('red'),
  };

  mockDrones.value.forEach(drone => {
    const currentMarkerContent = markerSVGContent[drone.status] || markerSVGContent.standby;

    const marker = new AMap.Marker({
      position: new AMap.LngLat(drone.position[0], drone.position[1]),
      title: drone.name,
      content: currentMarkerContent, // Use SVG content directly
      offset: new AMap.Pixel(-12, -36) // Offset for a 24x36 SVG pin (anchor bottom-center)
    });
    map.value.add(marker);

    marker.on('click', () => {
      let statusColor = 'blue'; // Default for standby
      if (drone.status === 'flying') statusColor = 'green';
      if (drone.status === 'offline') statusColor = 'red';

      const infoWindow = new AMap.InfoWindow({
        content: `<h3>${drone.name}</h3><p>状态: <span style="color:${statusColor}; font-weight:bold;">${drone.status.toUpperCase()}</span></p><p>ID: ${drone.id}</p><p>经纬度: ${drone.position.join(', ')}</p>`,
        offset: new AMap.Pixel(0, -30)
      });
      infoWindow.open(map.value, marker.getPosition());
    });
  });
};

onMounted(() => {
  initMap();
});

onUnmounted(() => {
  if (map.value) {
    map.value.destroy();
    map.value = null;
  }
});

</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 60px - 40px);
}

.box-card {
  margin-bottom: 20px;
}

.status-card {
  height: 400px; /* 根据内容调整或设为auto */
}

.map-card {
  height: calc(100vh - 60px - 40px - 40px); 
  min-height: 500px; 
  display: flex;
  flex-direction: column;
}

:deep(.map-card .el-card__body) {
  flex-grow: 1;
  padding: 0;
  display: flex;
}

.map-container-actual {
  width: 100%;
  height: 100%;
  flex-grow: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.status-overview .status-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.status-overview .status-item:last-child {
  border-bottom: none;
}

.status-label {
  color: #606266;
}

.status-value {
  font-weight: bold;
}

.status-value.online {
  color: #67c23a;
}

.status-value.flying {
  color: #67c23a;
}

.status-value.standby {
  color: #409eff;
}

.status-value.offline {
  color: #f56c6c;
}
</style> 