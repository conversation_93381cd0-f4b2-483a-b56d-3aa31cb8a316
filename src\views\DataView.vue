<template>
  <div class="news-view">
    <h1>国内新闻</h1>
    <div class="controls">
      <el-button @click="fetchNews(true)" :loading="loading && !lazyLoading">刷新新闻 (强制API)</el-button>
      <span v-if="allNewsFullyLoaded" class="all-loaded-text">已加载全部新闻</span>
    </div>
    <p class="api-notice">数据来源：聚合数据互联网新闻</p>
    <div v-if="error" class="error-message">{{ error }}</div>

    <div 
      class="news-list-container"
      v-infinite-scroll="loadMoreNews"
      :infinite-scroll-disabled="lazyScrollDisabled"
      :infinite-scroll-delay="500"
      :infinite-scroll-distance="10"
    >
      <el-row :gutter="20" v-if="displayNewsItems.length > 0" class="news-list">
        <el-col :span="24" v-for="item in displayNewsItems" :key="item.id" class="news-item-col-single">
          <el-card class="news-card single-news-card">
            <template v-if="item.picUrl">
              <div class="news-image-container single-news-image-container">
                <el-image 
                  class="news-image"
                  :src="item.picUrl" 
                  fit="contain"
                  lazy
                >
                  <template #placeholder>
                    <div class="image-slot">加载中<span class="dot">...</span></div>
                  </template>
                  <template #error>
                    <div class="image-slot">
                      <el-icon><icon-picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </template>
            <div class="news-content">
              <div class="card-header">
                <a :href="item.url" target="_blank" class="news-title-link" :title="item.title">{{ item.title }}</a>
              </div>
              <div class="news-meta">
                <p class="news-source">来源: {{ item.source }}</p>
                <p class="news-date">时间: {{ item.ctime }}</p>
              </div>
            </div>
            <div class="news-footer">
               <el-button type="primary" link @click="handleReadMore(item.id)">阅读原文</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <div v-if="lazyLoading" class="lazy-loading-spinner">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-if="allNewsFullyLoaded && displayNewsItems.length > 0 && !lazyLoading && !loading" class="all-loaded-footer">
        <span>--- 我是有底线的 ---</span>
      </div>
      <el-empty description="暂无新闻" v-if="!loading && !lazyLoading && displayNewsItems.length === 0 && !error" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';
import { ElMessage, ElSkeleton, ElIcon } from 'element-plus';
import { Picture as IconPicture } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router';

// 更新 NewsItem 接口以匹配API响应
interface NewsItem {
  id: string; // 使用 API 返回的 id 作为唯一标识
  ctime: string; 
  title: string;
  description?: string; // description 可能是空字符串
  source: string; 
  picUrl: string; // 图片URL
  url: string; 
}

const allFetchedNewsItems = ref<NewsItem[]>([]); // 存储从API获取的所有新闻
const displayNewsItems = ref<NewsItem[]>([]); // 当前显示在界面上的新闻
const loading = ref(false); // 初始/强制刷新加载状态
const lazyLoading = ref(false); // 懒加载进行中状态
const error = ref<string | null>(null);
const itemsPerLoad = 5; // 每次懒加载或初始加载显示的条数
const currentPage = ref(1); // 用于模拟分页加载，实际是切片索引

const router = useRouter();

const NEWS_API_KEY = '7856e527a126894a430cba5287eaf9cc';
const NEWS_API_URL = '/juhe/fapigx/internet_news/query';
const CACHE_DURATION_MS = 30 * 60 * 1000; // 缓存30分钟

const allNewsFullyLoaded = computed(() => displayNewsItems.value.length >= allFetchedNewsItems.value.length && allFetchedNewsItems.value.length > 0);
const lazyScrollDisabled = computed(() => loading.value || lazyLoading.value || allNewsFullyLoaded.value);

const processAndDisplayNews = (items: NewsItem[], isRefresh: boolean) => {
  if (isRefresh) {
    allFetchedNewsItems.value = items;
    displayNewsItems.value = []; // 清空当前显示的
    currentPage.value = 1;
  } else {
    // 如果不是刷新（即初始加载自缓存或API），也重置
    allFetchedNewsItems.value = items;
    displayNewsItems.value = [];
    currentPage.value = 1;
  }
  loadMoreNews(); // 加载第一批
};

const fetchNews = async (forceRefresh = false) => {
  if (loading.value) return; // 防止重复的强制刷新
  loading.value = true;
  lazyLoading.value = false; // 重置懒加载状态
  error.value = null;

  const cachedNews = localStorage.getItem('newsItemsList');
  const cachedTimestamp = localStorage.getItem('newsListTimestamp');

  if (!forceRefresh && cachedNews && cachedTimestamp) {
    const ageMs = Date.now() - parseInt(cachedTimestamp, 10);
    if (ageMs < CACHE_DURATION_MS) {
      processAndDisplayNews(JSON.parse(cachedNews), false);
      ElMessage.success('从缓存加载新闻成功！');
      loading.value = false;
      return;
    }
  }

  try {
    // 聚合这个API似乎不直接支持num或limit来控制返回数量，它通常返回固定数量(如10或20条)
    // 我们一次性获取，然后在前端分页展示
    const response = await axios.get(NEWS_API_URL, { params: { key: NEWS_API_KEY } });

    if (response.data && response.data.error_code === 0 && response.data.result && response.data.result.newslist) {
      const fetchedItems = response.data.result.newslist;
      localStorage.setItem('newsItemsList', JSON.stringify(fetchedItems));
      localStorage.setItem('newsListTimestamp', Date.now().toString());
      processAndDisplayNews(fetchedItems, forceRefresh || !cachedNews); // 如果是强制刷新或无缓存，视为刷新
      ElMessage.success(forceRefresh || !cachedNews ? '从API加载新闻成功！' : '缓存已更新，从API加载新闻成功！');
    } else if (response.data && response.data.error_code !== 0) {
      error.value = `API错误 (${response.data.error_code}): ${response.data.reason}`;
      ElMessage.error(error.value);
    } else {
      error.value = '无法解析新闻数据或API响应格式不正确。';
      ElMessage.error(error.value);
    }
  } catch (e: any) {
    console.error("新闻获取失败:", e);
    error.value = e.message || '新闻获取失败';
    ElMessage.error(error.value as string);
  } finally {
    loading.value = false;
  }
};

const loadMoreNews = () => {
  if (lazyLoading.value || allNewsFullyLoaded.value) return;
  
  lazyLoading.value = true;
  
  // 模拟网络延迟，让加载效果更明显
  setTimeout(() => {
    const start = (currentPage.value - 1) * itemsPerLoad;
    const end = currentPage.value * itemsPerLoad;
    const newItemsToDisplay = allFetchedNewsItems.value.slice(start, end);

    if (newItemsToDisplay.length > 0) {
      displayNewsItems.value.push(...newItemsToDisplay);
      currentPage.value++;
    }
    lazyLoading.value = false;
  }, 300); // 300ms延迟
};

const handleReadMore = (newsId: string) => {
  router.push({ name: 'news-detail', params: { id: newsId } });
};

onMounted(() => {
  fetchNews(); 
});

</script>

<style scoped>
.news-view {
  padding: 20px;
}
.api-notice {
  text-align: center;
  color: #909399; /* 颜色改得柔和一些 */
  margin-bottom: 15px;
  font-size: 0.9em;
}

.error-message {
  color: red;
  margin-top: 10px;
  margin-bottom: 10px;
  text-align: center;
}

.news-list-container {
  height: calc(100vh - 200px); /* 重新估算高度，确保滚动。可能需要根据您的实际页面顶部元素高度调整 */
  overflow-y: auto;
  padding: 0px 5px 5px 5px; /* 调整padding，左右可以小一些 */
}

.controls {
  margin-bottom: 15px;
  display:flex;
  align-items: center;
}

.all-loaded-text {
  margin-left: 15px;
  color: #909399;
  font-size: 0.9em;
}

.lazy-loading-spinner {
  text-align: center;
  padding: 20px;
}

.news-list {
  margin-top: 0; /* 如果父容器有padding，这里的margin可以去掉 */
}

.news-item-col-single {
  margin-bottom: 20px;
}

.single-news-card {
  /* display: flex; */ /* 单行显示时，标准块状布局可能更好，或者根据需要调整flex */
  /* flex-direction: row; */ /* 如果希望图片在左，文字在右 */
  max-width: 700px; /* 可以设置一个最大宽度，使其在宽屏上不至于太宽 */
  margin-left: auto;
  margin-right: auto;
}

/* 调整单行新闻图片容器和图片样式 */
.single-news-image-container {
  /* 如果是左右布局，可以设置固定宽度或flex-basis */
  /* width: 200px; */ 
  /* padding-top: 0; */ /* 如果不再使用宽高比技巧，则重置 */
  /* height: 120px; */ /* 或者设置一个固定高度 */
  /* margin-right: 15px; */
  /* float: left; */ /* 传统图文环绕 */
  width: 100%; /* 图片在卡片上方时，保持宽度100% */
  padding-top: 40%; /* 可以调整一个适合横向图片的宽高比，例如 2.5:1 */
  position: relative;
  background-color: #f0f2f5; 
}

.news-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
.image-slot .el-icon {
  font-size: 30px;
}

.news-content {
  padding: 14px;
  flex-grow: 1; /* 使内容区域填充剩余空间 */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* 使内容上下分布，页脚靠下 */
}

.news-card .card-header {
  margin-bottom: 8px;
}

.news-title-link {
  font-weight: bold;
  font-size: 1.05em; /* 略微调整标题大小 */
  color: #303133;
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 标题最多显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}
.news-title-link:hover {
  color: var(--el-color-primary);
}

.news-meta {
  margin-bottom: 10px; /* 来源和日期与页脚的间距 */
}

.news-source,
.news-date {
  font-size: 0.8em;
  color: #909399;
  line-height: 1.4; /* 调整行高 */
  margin-bottom: 2px; /* 来源和日期之间的细微间距 */
}

.news-footer {
  /* padding: 0 14px 14px; */ /* 由 news-content 的 padding 统一管理 */
  text-align: right;
  margin-top: auto; /* 将页脚推到底部 */
}

/* 确保没有意外的横向溢出 */
.news-view,
.news-list-container,
.news-list,
.news-item-col-single,
.single-news-card {
  box-sizing: border-box; /* 确保padding和border不增加额外宽度 */
  overflow-x: hidden; /* 强制隐藏横向滚动条 */
}

/* 新增：底线提示样式 */
.all-loaded-footer {
  text-align: center;
  padding: 20px 0;
  color: #c0c4cc; /* 浅灰色文字 */
  font-size: 0.9em;
}
</style> 